"""
Unit tests for the unified ingestion module.

Tests the UnifiedIngestionPipeline class with mixed sources (URLs and files).
"""

import pytest
import asyncio
import tempfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ingestion.unified_ingestion import (
    UnifiedIngestionPipeline, 
    ProgressTracker,
    ingest_mixed_sources
)


class TestUnifiedIngestionPipeline:
    """Test cases for UnifiedIngestionPipeline."""
    
    @pytest.fixture
    def pipeline(self):
        """Create a UnifiedIngestionPipeline instance for testing."""
        return UnifiedIngestionPipeline()
    
    @pytest.fixture
    def temp_file(self):
        """Create a temporary test file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Test content for unified pipeline")
            f.flush()
            yield f.name
        Path(f.name).unlink()
    
    def test_source_type_detection(self, pipeline):
        """Test URL vs file path detection."""
        # Test URLs
        assert pipeline._is_url("https://example.com") is True
        assert pipeline._is_url("http://test.org/page") is True
        assert pipeline._is_url("ftp://server.com") is False  # Only HTTP(S)
        assert pipeline._is_url("/path/to/file") is False
        assert pipeline._is_url("C:\\Windows\\file.txt") is False
        
        # Test file paths
        assert pipeline._is_file_or_directory("/tmp") is True  # Exists on most systems
        assert pipeline._is_file_or_directory("/nonexistent/path") is False
    
    @pytest.mark.asyncio
    async def test_ingest_file(self, pipeline, temp_file):
        """Test ingesting a local file."""
        result = await pipeline.ingest(temp_file)
        
        assert result['success'] is True
        assert 'Test content for unified pipeline' in result['content']
        assert result['metadata']['source_type'] == 'file'
        assert pipeline.stats['file_sources'] == 1
        assert pipeline.stats['total_processed'] == 1
    
    @pytest.mark.asyncio
    async def test_ingest_web_url_mock(self, pipeline):
        """Test ingesting a web URL with mocked crawler."""
        mock_result = {
            'url': 'https://example.com',
            'content': 'Mocked web content',
            'metadata': {'source_type': 'web'},
            'success': True,
            'error': None
        }
        
        with patch('enhanced_rag.ingestion.unified_ingestion.crawl_single_page', 
                   new_callable=AsyncMock) as mock_crawl:
            mock_crawl.return_value = mock_result
            
            result = await pipeline.ingest('https://example.com')
            
            assert result == mock_result
            assert pipeline.stats['web_sources'] == 1
            assert pipeline.stats['total_processed'] == 1
    
    @pytest.mark.asyncio
    async def test_invalid_source(self, pipeline):
        """Test handling of invalid source."""
        with pytest.raises(ValueError, match="Cannot determine source type"):
            await pipeline.ingest("not-a-url-or-file")
    
    @pytest.mark.asyncio
    async def test_disabled_web_crawling(self):
        """Test error when web crawling is disabled."""
        pipeline = UnifiedIngestionPipeline(enable_web_crawling=False)
        
        with pytest.raises(ValueError, match="Web crawling is disabled"):
            await pipeline.ingest("https://example.com")
    
    @pytest.mark.asyncio
    async def test_disabled_file_ingestion(self, temp_file):
        """Test error when file ingestion is disabled."""
        pipeline = UnifiedIngestionPipeline(enable_file_ingestion=False)
        
        with pytest.raises(ValueError, match="File ingestion is disabled"):
            await pipeline.ingest(temp_file)
    
    @pytest.mark.asyncio
    async def test_batch_ingest_mixed_sources(self, pipeline, temp_file):
        """Test batch ingestion with mixed sources."""
        sources = [
            temp_file,  # File
            "/nonexistent/file.txt",  # Will fail
        ]
        
        # Mock web crawling
        with patch('enhanced_rag.ingestion.unified_ingestion.crawl_single_page', 
                   new_callable=AsyncMock) as mock_crawl:
            mock_crawl.return_value = {
                'url': 'https://example.com',
                'content': 'Web content',
                'success': True
            }
            
            # Add a web source if mocking is available
            sources.append("https://example.com")
            
            results = await pipeline.batch_ingest(sources, continue_on_error=True)
            
            assert len(results) == 3
            # First should succeed (temp file)
            assert results[0]['success'] is True
            # Second should fail (nonexistent)
            assert results[1]['success'] is False
            # Third should succeed (mocked web)
            assert results[2]['success'] is True
    
    @pytest.mark.asyncio
    async def test_batch_ingest_concurrency(self, pipeline):
        """Test concurrent processing limits."""
        # Create multiple temp files
        temp_files = []
        for i in range(10):
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write(f"Content {i}")
                f.flush()
                temp_files.append(f.name)
        
        try:
            # Process with limited concurrency
            results = await pipeline.batch_ingest(temp_files, max_concurrent=3)
            
            assert len(results) == 10
            assert all(r['success'] for r in results)
            assert pipeline.stats['file_sources'] == 10
        
        finally:
            # Cleanup
            for f in temp_files:
                Path(f).unlink()
    
    def test_statistics_tracking(self, pipeline):
        """Test statistics tracking and reset."""
        # Manually update stats
        pipeline.stats['total_processed'] = 5
        pipeline.stats['web_sources'] = 2
        pipeline.stats['file_sources'] = 3
        
        stats = pipeline.get_stats()
        assert stats['total_processed'] == 5
        assert stats['web_sources'] == 2
        assert stats['file_sources'] == 3
        
        # Reset stats
        pipeline.reset_stats()
        stats = pipeline.get_stats()
        assert all(v == 0 for v in stats.values())


class TestProgressTracker:
    """Test cases for ProgressTracker."""
    
    def test_progress_tracking(self):
        """Test progress tracking functionality."""
        callback_calls = []
        
        def callback(current, total, message):
            callback_calls.append((current, total, message))
        
        tracker = ProgressTracker(total=3, callback=callback)
        
        # Update progress
        tracker.update("file1.txt", success=True)
        tracker.update("file2.txt", success=False, error="Failed to read")
        tracker.update("file3.txt", success=True)
        
        # Check callback was called
        assert len(callback_calls) == 3
        assert callback_calls[0][0] == 1  # Current
        assert callback_calls[0][1] == 3  # Total
        
        # Check summary
        summary = tracker.get_summary()
        assert summary['total'] == 3
        assert summary['completed'] == 2
        assert summary['errors'] == 1
        assert len(summary['error_details']) == 1
        assert summary['error_details'][0][0] == "file2.txt"


class TestUtilityFunctions:
    """Test utility functions."""
    
    @pytest.mark.asyncio
    async def test_ingest_mixed_sources(self):
        """Test the convenience function for mixed source ingestion."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Test content")
            f.flush()
            
            try:
                # Mock web crawling
                with patch('enhanced_rag.ingestion.unified_ingestion.crawl_single_page', 
                           new_callable=AsyncMock) as mock_crawl:
                    mock_crawl.return_value = {
                        'url': 'https://example.com',
                        'content': 'Web content',
                        'success': True
                    }
                    
                    sources = [f.name, "https://example.com"]
                    results = await ingest_mixed_sources(sources)
                    
                    assert len(results) == 2
                    assert results[0]['success'] is True  # File
                    assert results[1]['success'] is True  # Web
            
            finally:
                Path(f.name).unlink()


if __name__ == '__main__':
    pytest.main([__file__, '-v'])