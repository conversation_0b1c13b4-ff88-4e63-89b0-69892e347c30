"""
Unit tests for the file ingestion module.

Tests the FileIngestionHandler class with various file types and edge cases.
"""

import pytest
import asyncio
import tempfile
from pathlib import Path
from datetime import datetime
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ingestion.file_ingestion import FileIngestionHandler, ingest_single_file


class TestFileIngestionHandler:
    """Test cases for FileIngestionHandler."""
    
    @pytest.fixture
    def handler(self):
        """Create a FileIngestionHandler instance for testing."""
        return FileIngestionHandler(max_file_size_mb=10)
    
    @pytest.fixture
    def temp_files(self):
        """Create temporary test files."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create test PDF content (mock)
            pdf_path = Path(tmpdir) / "test.pdf"
            pdf_path.write_text("Mock PDF content for testing")
            
            # Create test Markdown file
            md_path = Path(tmpdir) / "test.md"
            md_path.write_text("""# Test Document

This is a test markdown document.

## Section 1
Some content here.

## Section 2
More content with a [link](https://example.com).

```python
def hello():
    print("Hello, world!")
```

| Column 1 | Column 2 |
|----------|----------|
| Data 1   | Data 2   |
""")
            
            # Create test text file
            txt_path = Path(tmpdir) / "test.txt"
            txt_path.write_text("This is a simple text file.")
            
            # Create large file (over limit)
            large_path = Path(tmpdir) / "large.txt"
            large_path.write_text("x" * (11 * 1024 * 1024))  # 11MB
            
            yield {
                'pdf': str(pdf_path),
                'markdown': str(md_path),
                'text': str(txt_path),
                'large': str(large_path),
                'dir': tmpdir
            }
    
    @pytest.mark.asyncio
    async def test_ingest_markdown_file(self, handler, temp_files):
        """Test ingesting a markdown file."""
        result = await handler.ingest_file(temp_files['markdown'])
        
        assert result['success'] is True
        assert result['error'] is None
        assert 'Test Document' in result['content']
        assert 'Section 1' in result['content']
        
        metadata = result['metadata']
        assert metadata['source_type'] == 'file'
        assert metadata['file_type'] == 'md'
        assert metadata['file_name'] == 'test.md'
        assert metadata['has_tables'] is True  # Markdown contains a table
        assert metadata['page_count'] == 1
    
    @pytest.mark.asyncio
    async def test_ingest_text_file(self, handler, temp_files):
        """Test ingesting a text file."""
        result = await handler.ingest_file(temp_files['text'])
        
        assert result['success'] is True
        assert 'simple text file' in result['content']
        assert result['metadata']['file_type'] == 'txt'
    
    @pytest.mark.asyncio
    async def test_file_not_found(self, handler):
        """Test handling of non-existent file."""
        with pytest.raises(FileNotFoundError):
            await handler.ingest_file('/non/existent/file.pdf')
    
    @pytest.mark.asyncio
    async def test_file_too_large(self, handler, temp_files):
        """Test handling of file exceeding size limit."""
        with pytest.raises(ValueError, match="File too large"):
            await handler.ingest_file(temp_files['large'])
    
    @pytest.mark.asyncio
    async def test_unsupported_file_type(self, handler):
        """Test handling of unsupported file type."""
        with tempfile.NamedTemporaryFile(suffix='.xyz', mode='w') as f:
            f.write("content")
            f.flush()
            
            with pytest.raises(ValueError, match="Unsupported file type"):
                await handler.ingest_file(f.name)
    
    @pytest.mark.asyncio
    async def test_ingest_directory(self, handler, temp_files):
        """Test ingesting all files from a directory."""
        results = await handler.ingest_directory(temp_files['dir'], recursive=False)
        
        # Should have processed multiple files (excluding the large one if it fails)
        assert len(results) >= 3
        
        # Check that files were processed
        file_names = [r['metadata']['file_name'] for r in results if r['success']]
        assert 'test.md' in file_names
        assert 'test.txt' in file_names
    
    def test_supports_extension(self, handler):
        """Test extension support checking."""
        assert handler.supports_extension('.pdf') is True
        assert handler.supports_extension('pdf') is True
        assert handler.supports_extension('.md') is True
        assert handler.supports_extension('.xyz') is False
    
    def test_get_supported_extensions(self, handler):
        """Test getting list of supported extensions."""
        extensions = handler.get_supported_extensions()
        assert '.pdf' in extensions
        assert '.md' in extensions
        assert '.docx' in extensions
        assert '.csv' in extensions
        assert '.txt' in extensions
    
    @pytest.mark.asyncio
    async def test_file_hash_generation(self, handler, temp_files):
        """Test that file hash is generated correctly."""
        result = await handler.ingest_file(temp_files['text'])
        
        assert 'file_hash' in result['metadata']
        assert len(result['metadata']['file_hash']) == 64  # SHA256 hash length
        
        # Hash should be consistent
        result2 = await handler.ingest_file(temp_files['text'])
        assert result['metadata']['file_hash'] == result2['metadata']['file_hash']
    
    @pytest.mark.asyncio
    async def test_metadata_extraction(self, handler, temp_files):
        """Test comprehensive metadata extraction."""
        result = await handler.ingest_file(temp_files['markdown'])
        metadata = result['metadata']
        
        # Check required metadata fields
        required_fields = [
            'source_type', 'source_url', 'file_path', 'file_name',
            'file_type', 'file_size', 'file_hash', 'last_modified',
            'extraction_date', 'mime_type', 'page_count', 'has_tables',
            'has_images', 'extraction_method'
        ]
        
        for field in required_fields:
            assert field in metadata, f"Missing metadata field: {field}"
        
        # Validate metadata values
        assert metadata['source_type'] == 'file'
        assert metadata['source_url'].startswith('file://')
        assert metadata['extraction_method'] == 'llama_index'
        assert isinstance(metadata['file_size'], int)
        assert metadata['file_size'] > 0
    
    @pytest.mark.asyncio
    async def test_table_detection(self, handler):
        """Test table detection in content."""
        # Create file with table
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write("""
# Document with Table

| Header 1 | Header 2 |
|----------|----------|
| Cell 1   | Cell 2   |
""")
            f.flush()
            
            result = await handler.ingest_file(f.name)
            assert result['metadata']['has_tables'] is True
            
            Path(f.name).unlink()
        
        # Create file without table
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write("# Simple document\n\nNo tables here.")
            f.flush()
            
            result = await handler.ingest_file(f.name)
            assert result['metadata']['has_tables'] is False
            
            Path(f.name).unlink()
    
    @pytest.mark.asyncio
    async def test_concurrent_ingestion(self, handler, temp_files):
        """Test concurrent file ingestion."""
        # Create multiple files
        files = [temp_files['markdown'], temp_files['text']]
        
        # Ingest concurrently
        tasks = [handler.ingest_file(f) for f in files]
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 2
        assert all(r['success'] for r in results)


class TestUtilityFunctions:
    """Test utility functions."""
    
    @pytest.mark.asyncio
    async def test_ingest_single_file_utility(self, temp_files):
        """Test the convenience function for single file ingestion."""
        result = await ingest_single_file(temp_files['text'])
        
        assert result['success'] is True
        assert 'simple text file' in result['content']


if __name__ == '__main__':
    pytest.main([__file__, '-v'])