# Enhanced RAG System Requirements
# Core dependencies for file ingestion and processing

# LlamaIndex core and readers
llama-index>=0.9.0
llama-index-core>=0.9.0
llama-index-readers-file>=0.1.0
llama-index-llms-openai>=0.1.0
llama-index-embeddings-openai>=0.1.0

# PDF processing
PyPDF2>=3.0.0
pdfplumber>=0.10.0
pypdf>=3.17.0

# Document processing
python-docx>=1.0.0
python-frontmatter>=1.0.0
markdown>=3.5.0
pandas>=2.0.0  # For CSV support

# Web crawling (existing dependency)
crawl4ai>=0.2.0  # If available, otherwise remove

# Storage and database
supabase>=2.0.0
qdrant-client>=1.7.0

# Async support
aiofiles>=23.0.0
asyncio>=3.4.3

# Utilities
python-magic>=0.4.27  # For MIME type detection
tqdm>=4.66.0  # Progress bars
click>=8.1.0  # CLI support

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0

# Development
black>=23.0.0
mypy>=1.7.0
ruff>=0.1.0