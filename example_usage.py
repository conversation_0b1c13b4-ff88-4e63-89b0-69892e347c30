"""
Example usage of the Enhanced RAG System

This script demonstrates how to use the unified ingestion pipeline to process
both web URLs and local files (PDFs, Markdown, etc.) in a single workflow.
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import List, Dict, Any

# Add enhanced_rag to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ingestion.unified_ingestion import UnifiedIngestionPipeline, ProgressTracker
from ingestion.file_ingestion import FileIngestionHandler


async def basic_example():
    """Basic example of ingesting different source types."""
    print("=== Basic Usage Example ===\n")
    
    # Initialize the unified pipeline
    pipeline = UnifiedIngestionPipeline()
    
    # Example sources (mix of files and URLs)
    sources = [
        # Local files
        "example_files/sample.pdf",      # PDF document
        "example_files/notes.md",        # Markdown file
        "example_files/data.csv",        # CSV file
        
        # Web URLs
        "https://example.com/article",   # Web page
        "https://example.com/sitemap.xml",  # Sitemap
    ]
    
    # Process each source
    for source in sources:
        try:
            print(f"Processing: {source}")
            result = await pipeline.ingest(source)
            
            if result['success']:
                metadata = result['metadata']
                print(f"  ✓ Success - Type: {metadata.get('file_type', 'web')}")
                print(f"  Content preview: {result['content'][:100]}...")
                print(f"  Source type: {metadata['source_type']}")
                if metadata['source_type'] == 'file':
                    print(f"  File size: {metadata['file_size'] / 1024:.2f} KB")
                    print(f"  Pages: {metadata.get('page_count', 1)}")
            else:
                print(f"  ✗ Failed: {result['error']}")
        
        except Exception as e:
            print(f"  ✗ Error: {str(e)}")
        
        print()


async def batch_processing_example():
    """Example of batch processing with progress tracking."""
    print("\n=== Batch Processing Example ===\n")
    
    pipeline = UnifiedIngestionPipeline()
    
    # Multiple sources to process
    sources = [
        "documents/report1.pdf",
        "documents/report2.pdf",
        "notes/meeting1.md",
        "notes/meeting2.md",
        "https://example.com/page1",
        "https://example.com/page2",
    ]
    
    # Progress callback
    def progress_callback(current, total, message):
        progress = (current / total) * 100
        print(f"[{progress:3.0f}%] {current}/{total} - {message}")
    
    # Create progress tracker
    tracker = ProgressTracker(len(sources), progress_callback)
    
    # Process with concurrency limit
    print("Processing batch with max 3 concurrent operations...")
    results = await pipeline.batch_ingest(
        sources, 
        max_concurrent=3,
        continue_on_error=True
    )
    
    # Summary
    successful = sum(1 for r in results if r.get('success', False))
    print(f"\nBatch complete: {successful}/{len(results)} successful")
    
    # Show statistics
    stats = pipeline.get_stats()
    print(f"\nStatistics:")
    print(f"  Total processed: {stats['total_processed']}")
    print(f"  Web sources: {stats['web_sources']}")
    print(f"  File sources: {stats['file_sources']}")
    print(f"  Errors: {stats['errors']}")


async def directory_ingestion_example():
    """Example of ingesting an entire directory."""
    print("\n=== Directory Ingestion Example ===\n")
    
    # Create file handler with custom settings
    file_handler = FileIngestionHandler(
        max_file_size_mb=50,
        extract_tables=True,
        extract_images=False  # Skip image extraction for speed
    )
    
    pipeline = UnifiedIngestionPipeline(file_handler=file_handler)
    
    # Ingest entire directory
    directory = "research_papers/"
    print(f"Ingesting all supported files from: {directory}")
    
    try:
        results = await pipeline.ingest(
            directory,
            recursive=True,
            file_extensions=['.pdf', '.md', '.txt']
        )
        
        if isinstance(results, list):
            print(f"Found and processed {len(results)} files")
            
            # Group by file type
            by_type = {}
            for result in results:
                if result['success']:
                    file_type = result['metadata']['file_type']
                    by_type[file_type] = by_type.get(file_type, 0) + 1
            
            print("\nFiles by type:")
            for file_type, count in by_type.items():
                print(f"  {file_type}: {count}")
        
    except Exception as e:
        print(f"Error: {str(e)}")


async def advanced_filtering_example():
    """Example with advanced filtering and metadata extraction."""
    print("\n=== Advanced Filtering Example ===\n")
    
    pipeline = UnifiedIngestionPipeline()
    
    # Process a file and examine metadata
    source = "research_papers/machine_learning.pdf"
    
    print(f"Processing: {source}")
    result = await pipeline.ingest(source)
    
    if result['success']:
        metadata = result['metadata']
        
        print("\nExtracted Metadata:")
        print(f"  File name: {metadata['file_name']}")
        print(f"  File type: {metadata['file_type']}")
        print(f"  File size: {metadata['file_size'] / 1024:.2f} KB")
        print(f"  Page count: {metadata.get('page_count', 'N/A')}")
        print(f"  Has tables: {metadata.get('has_tables', False)}")
        print(f"  Has images: {metadata.get('has_images', False)}")
        print(f"  File hash: {metadata['file_hash'][:16]}...")
        print(f"  Last modified: {metadata['last_modified']}")
        
        # If PDF with multiple pages, show page info
        if metadata.get('pages'):
            print(f"\n  Page details ({len(metadata['pages'])} pages):")
            for i, page in enumerate(metadata['pages'][:3]):  # First 3 pages
                print(f"    Page {page['page_number']}: {page['content_preview']}")
            if len(metadata['pages']) > 3:
                print(f"    ... and {len(metadata['pages']) - 3} more pages")


async def integration_with_existing_pipeline():
    """Example showing integration with existing RAG pipeline."""
    print("\n=== Integration with Existing Pipeline ===\n")
    
    # This example shows how the enhanced system integrates with existing code
    pipeline = UnifiedIngestionPipeline()
    
    # The output format is compatible with existing pipeline
    sources = [
        "local_file.pdf",           # New capability
        "https://example.com/doc",  # Existing capability
    ]
    
    for source in sources:
        try:
            # Same interface for both file and web sources
            result = await pipeline.ingest(source)
            
            # Result format matches existing pipeline expectations
            assert 'url' in result
            assert 'content' in result
            assert 'metadata' in result
            assert 'success' in result
            assert 'error' in result
            
            print(f"✓ {source} - Compatible format verified")
            
            # The result can be passed to existing storage/processing code
            # existing_storage.store(result)  # Would work with existing code
            
        except Exception as e:
            print(f"✗ {source} - Error: {str(e)}")


def create_sample_files():
    """Create sample files for testing (if they don't exist)."""
    samples_dir = Path("example_files")
    samples_dir.mkdir(exist_ok=True)
    
    # Create sample markdown
    md_file = samples_dir / "notes.md"
    if not md_file.exists():
        md_file.write_text("""# Sample Notes

## Overview
This is a sample markdown file for testing the enhanced RAG system.

## Features
- Supports **bold** and *italic* text
- Lists and nested structures
- Code blocks

```python
def hello():
    print("Hello from enhanced RAG!")
```

## Data Table

| Feature | Status |
|---------|--------|
| PDF Support | ✓ |
| Markdown | ✓ |
| Web Crawling | ✓ |
""")
    
    # Create sample CSV
    csv_file = samples_dir / "data.csv"
    if not csv_file.exists():
        csv_file.write_text("""Name,Type,Status
File Ingestion,Feature,Completed
Unified Pipeline,Feature,Completed
PDF Support,Enhancement,Active
OCR Support,Enhancement,Planned
""")
    
    print(f"Sample files created in: {samples_dir}")


async def main():
    """Run all examples."""
    # Create sample files if needed
    create_sample_files()
    
    # Run examples
    print("Enhanced RAG System - Usage Examples")
    print("=" * 50)
    
    # Note: Some examples use placeholder paths that may not exist
    # Replace with actual file paths for real usage
    
    try:
        # Basic usage
        await basic_example()
        
        # Batch processing
        # await batch_processing_example()
        
        # Directory ingestion
        # await directory_ingestion_example()
        
        # Advanced filtering
        # await advanced_filtering_example()
        
        # Integration example
        await integration_with_existing_pipeline()
        
    except Exception as e:
        print(f"\nError in examples: {str(e)}")
        print("Note: Some examples use placeholder file paths.")
        print("Replace with actual files/URLs for real usage.")


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())