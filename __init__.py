"""
Enhanced RAG System

A modular extension to the existing RAG pipeline that adds support for
local file ingestion (PDF, Markdown, etc.) while maintaining full
compatibility with the web-based crawling system.
"""

__version__ = "0.1.0"

from .ingestion.file_ingestion import FileIngestionHandler, ingest_single_file
from .ingestion.unified_ingestion import (
    UnifiedIngestionPipeline,
    ProgressTracker,
    ingest_mixed_sources,
    ingest_with_progress
)

__all__ = [
    'FileIngestionHandler',
    'UnifiedIngestionPipeline',
    'ProgressTracker',
    'ingest_single_file',
    'ingest_mixed_sources',
    'ingest_with_progress'
]