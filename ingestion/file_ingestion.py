"""
File Ingestion Module for Local Documents

This module handles ingestion of various file types using LlamaIndex readers.
It provides a unified interface for processing PDFs, Markdown, and other document formats,
converting them to a format compatible with the existing RAG pipeline.
"""

import logging
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from datetime import datetime
import hashlib
import mimetypes

from llama_index.core import Document
from llama_index.core.readers import SimpleDirectoryReader
from llama_index.readers.file import (
    PDFReader,
    MarkdownReader,
    DocxReader,
    CSVReader,
    UnstructuredReader
)

logger = logging.getLogger(__name__)


class FileIngestionHandler:
    """
    Handles ingestion of various file types using LlamaIndex readers.
    
    This class provides methods to read and process different document formats,
    extracting content and metadata for storage in the RAG system.
    """
    
    def __init__(self, 
                 max_file_size_mb: int = 100,
                 extract_images: bool = False,
                 extract_tables: bool = True):
        """
        Initialize the file ingestion handler.
        
        Args:
            max_file_size_mb: Maximum file size in MB to process
            extract_images: Whether to extract images from documents
            extract_tables: Whether to extract tables from documents
        """
        self.max_file_size_bytes = max_file_size_mb * 1024 * 1024
        self.extract_images = extract_images
        self.extract_tables = extract_tables
        
        # Initialize readers for different file types
        self.readers = {
            '.pdf': PDFReader(return_full_document=False),
            '.md': MarkdownReader(),
            '.markdown': MarkdownReader(),
            '.docx': DocxReader(),
            '.csv': CSVReader(),
            '.txt': UnstructuredReader()
        }
    
    async def ingest_file(self, file_path: str) -> Dict[str, Any]:
        """
        Ingest a single file and convert to pipeline format.
        
        Args:
            file_path: Path to the file to ingest
            
        Returns:
            Dictionary containing processed content and metadata
            
        Raises:
            ValueError: If file type is not supported or file is too large
            FileNotFoundError: If file does not exist
        """
        path = Path(file_path)
        
        # Validate file
        if not path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        if path.stat().st_size > self.max_file_size_bytes:
            raise ValueError(f"File too large: {path.stat().st_size / 1024 / 1024:.2f}MB exceeds limit of {self.max_file_size_bytes / 1024 / 1024}MB")
        
        extension = path.suffix.lower()
        if extension not in self.readers:
            raise ValueError(f"Unsupported file type: {extension}")
        
        logger.info(f"Ingesting file: {file_path} (type: {extension})")
        
        # Read file using appropriate reader
        try:
            reader = self.readers[extension]
            documents = reader.load_data(str(path))
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {str(e)}")
            raise
        
        # Convert to pipeline format
        return await self._convert_to_pipeline_format(documents, file_path)
    
    async def ingest_directory(self, directory_path: str, 
                             recursive: bool = True,
                             file_extensions: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Ingest all supported files from a directory.
        
        Args:
            directory_path: Path to directory containing files
            recursive: Whether to process subdirectories
            file_extensions: List of extensions to process (None for all supported)
            
        Returns:
            List of processed documents
        """
        path = Path(directory_path)
        if not path.is_dir():
            raise ValueError(f"Not a directory: {directory_path}")
        
        # Use SimpleDirectoryReader for batch processing
        if file_extensions is None:
            file_extensions = list(self.readers.keys())
        
        reader = SimpleDirectoryReader(
            input_dir=str(path),
            recursive=recursive,
            file_extractor=self.readers,
            required_exts=file_extensions
        )
        
        documents = reader.load_data()
        
        # Process all documents
        results = []
        for doc in documents:
            file_path = doc.metadata.get('file_path', doc.metadata.get('source', 'unknown'))
            result = await self._convert_to_pipeline_format([doc], file_path)
            results.append(result)
        
        return results
    
    async def _convert_to_pipeline_format(self, documents: List[Document], 
                                        file_path: str) -> Dict[str, Any]:
        """
        Convert LlamaIndex documents to existing pipeline format.
        
        Args:
            documents: List of LlamaIndex Document objects
            file_path: Original file path
            
        Returns:
            Dictionary compatible with existing RAG pipeline
        """
        path = Path(file_path)
        file_stats = path.stat()
        
        # Generate file hash for deduplication
        file_hash = self._generate_file_hash(file_path)
        
        # Combine all document content
        content_parts = []
        metadata_parts = []
        
        for i, doc in enumerate(documents):
            content_parts.append(doc.text)
            
            # Extract page-level metadata for PDFs
            if path.suffix.lower() == '.pdf' and 'page_label' in doc.metadata:
                metadata_parts.append({
                    'page_number': doc.metadata.get('page_label', i + 1),
                    'content_preview': doc.text[:200] + '...' if len(doc.text) > 200 else doc.text
                })
        
        # Build comprehensive metadata
        metadata = {
            'source_type': 'file',
            'source_url': f'file://{path.absolute()}',
            'file_path': str(path.absolute()),
            'file_name': path.name,
            'file_type': path.suffix.lower()[1:],  # Remove the dot
            'file_size': file_stats.st_size,
            'file_hash': file_hash,
            'last_modified': datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
            'extraction_date': datetime.now().isoformat(),
            'mime_type': mimetypes.guess_type(file_path)[0],
            'page_count': len(documents) if path.suffix.lower() == '.pdf' else 1,
            'has_tables': self._detect_tables(content_parts),
            'has_images': self._detect_images(documents),
            'extraction_method': 'llama_index',
            'pages': metadata_parts if metadata_parts else None
        }
        
        # Format for existing pipeline
        return {
            'url': metadata['source_url'],
            'content': '\n\n'.join(content_parts),
            'metadata': metadata,
            'success': True,
            'error': None,
            'extracted_at': metadata['extraction_date']
        }
    
    def _generate_file_hash(self, file_path: str) -> str:
        """Generate SHA256 hash of file for deduplication."""
        sha256_hash = hashlib.sha256()
        with open(file_path, 'rb') as f:
            for byte_block in iter(lambda: f.read(4096), b''):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()
    
    def _detect_tables(self, content_parts: List[str]) -> bool:
        """Detect if content likely contains tables."""
        table_indicators = ['|', '┌', '┐', '└', '┘', '─', '│', 'table', 'Table']
        full_content = ' '.join(content_parts).lower()
        return any(indicator in full_content for indicator in table_indicators)
    
    def _detect_images(self, documents: List[Document]) -> bool:
        """Detect if documents contain images."""
        for doc in documents:
            if 'images' in doc.metadata and doc.metadata['images']:
                return True
            # Check content for image references
            if any(pattern in doc.text.lower() for pattern in ['![', '<img', 'figure', 'image']):
                return True
        return False
    
    def supports_extension(self, extension: str) -> bool:
        """Check if file extension is supported."""
        if not extension.startswith('.'):
            extension = f'.{extension}'
        return extension.lower() in self.readers
    
    def get_supported_extensions(self) -> List[str]:
        """Get list of supported file extensions."""
        return list(self.readers.keys())


# Utility functions for standalone usage
async def ingest_single_file(file_path: str, **kwargs) -> Dict[str, Any]:
    """
    Convenience function to ingest a single file.
    
    Args:
        file_path: Path to file
        **kwargs: Additional arguments for FileIngestionHandler
        
    Returns:
        Processed document dictionary
    """
    handler = FileIngestionHandler(**kwargs)
    return await handler.ingest_file(file_path)


async def ingest_directory(directory_path: str, **kwargs) -> List[Dict[str, Any]]:
    """
    Convenience function to ingest all files in a directory.
    
    Args:
        directory_path: Path to directory
        **kwargs: Additional arguments for FileIngestionHandler
        
    Returns:
        List of processed documents
    """
    handler = FileIngestionHandler(**kwargs)
    return await handler.ingest_directory(directory_path)