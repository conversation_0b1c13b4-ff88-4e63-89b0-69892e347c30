"""
Unified Ingestion Pipeline

This module provides a single interface for ingesting content from both web URLs
and local files. It automatically detects the source type and routes to the
appropriate handler while maintaining a consistent output format.
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from urllib.parse import urlparse
import os
import sys

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from enhanced_rag.ingestion.file_ingestion import FileIngestionHandler

# Import from existing pipeline - adjust path as needed
try:
    from rag_components.ingestion.web_crawling import crawl_single_page, smart_crawl_url
except ImportError:
    # Fallback for testing without full system
    logger = logging.getLogger(__name__)
    logger.warning("Could not import web crawling modules. Web ingestion will not be available.")
    crawl_single_page = None
    smart_crawl_url = None

logger = logging.getLogger(__name__)


class UnifiedIngestionPipeline:
    """
    Unified pipeline that handles both web URLs and local files transparently.
    
    This class provides a single interface for content ingestion, automatically
    detecting whether the input is a URL or file path and routing to the
    appropriate handler.
    """
    
    def __init__(self,
                 file_handler: Optional[FileIngestionHandler] = None,
                 max_file_size_mb: int = 100,
                 enable_web_crawling: bool = True,
                 enable_file_ingestion: bool = True):
        """
        Initialize the unified ingestion pipeline.
        
        Args:
            file_handler: Custom file handler instance (creates default if None)
            max_file_size_mb: Maximum file size for processing
            enable_web_crawling: Whether to enable web URL ingestion
            enable_file_ingestion: Whether to enable local file ingestion
        """
        self.file_handler = file_handler or FileIngestionHandler(
            max_file_size_mb=max_file_size_mb
        )
        self.enable_web_crawling = enable_web_crawling
        self.enable_file_ingestion = enable_file_ingestion
        
        # Track processing statistics
        self.stats = {
            'total_processed': 0,
            'web_sources': 0,
            'file_sources': 0,
            'errors': 0
        }
    
    async def ingest(self, source: str, **kwargs) -> Dict[str, Any]:
        """
        Ingest content from a source (URL or file path).
        
        Args:
            source: URL or file path to ingest
            **kwargs: Additional arguments passed to specific handlers
            
        Returns:
            Dictionary containing processed content and metadata
            
        Raises:
            ValueError: If source type cannot be determined or is disabled
        """
        source = source.strip()
        
        # Detect source type
        if self._is_url(source):
            if not self.enable_web_crawling:
                raise ValueError("Web crawling is disabled")
            return await self._ingest_web(source, **kwargs)
        elif self._is_file_or_directory(source):
            if not self.enable_file_ingestion:
                raise ValueError("File ingestion is disabled")
            return await self._ingest_file_or_directory(source, **kwargs)
        else:
            raise ValueError(f"Cannot determine source type for: {source}")
    
    async def batch_ingest(self, sources: List[str], 
                          max_concurrent: int = 5,
                          continue_on_error: bool = True) -> List[Dict[str, Any]]:
        """
        Ingest multiple sources concurrently.
        
        Args:
            sources: List of URLs or file paths
            max_concurrent: Maximum concurrent operations
            continue_on_error: Whether to continue if some sources fail
            
        Returns:
            List of results (successful and failed)
        """
        logger.info(f"Starting batch ingestion of {len(sources)} sources")
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_with_semaphore(source: str) -> Dict[str, Any]:
            async with semaphore:
                try:
                    result = await self.ingest(source)
                    return result
                except Exception as e:
                    logger.error(f"Error processing {source}: {str(e)}")
                    self.stats['errors'] += 1
                    
                    error_result = {
                        'url': source,
                        'content': '',
                        'metadata': {'source': source},
                        'success': False,
                        'error': str(e)
                    }
                    
                    if not continue_on_error:
                        raise
                    return error_result
        
        # Process all sources concurrently
        tasks = [process_with_semaphore(source) for source in sources]
        results = await asyncio.gather(*tasks, return_exceptions=continue_on_error)
        
        # Filter out exceptions if we continued on error
        if continue_on_error:
            results = [r if not isinstance(r, Exception) else {
                'url': sources[i],
                'success': False,
                'error': str(r)
            } for i, r in enumerate(results)]
        
        logger.info(f"Batch ingestion complete. Processed: {len(results)}, Errors: {self.stats['errors']}")
        return results
    
    def _is_url(self, source: str) -> bool:
        """Check if source is a valid URL."""
        try:
            result = urlparse(source)
            return all([result.scheme, result.netloc]) and result.scheme in ['http', 'https']
        except:
            return False
    
    def _is_file_or_directory(self, source: str) -> bool:
        """Check if source is a valid file or directory path."""
        path = Path(source)
        return path.exists()
    
    async def _ingest_web(self, url: str, **kwargs) -> Dict[str, Any]:
        """Ingest content from a web URL."""
        logger.info(f"Ingesting web content from: {url}")
        self.stats['web_sources'] += 1
        self.stats['total_processed'] += 1
        
        if crawl_single_page is None:
            raise ImportError("Web crawling modules not available")
        
        # Determine crawling strategy
        if url.endswith('.xml') or 'sitemap' in url:
            # Use smart crawl for sitemaps
            result = await smart_crawl_url(
                url=url,
                max_depth=kwargs.get('max_depth', 1),
                max_concurrent=kwargs.get('max_concurrent', 10)
            )
        else:
            # Single page crawl
            result = await crawl_single_page(url=url)
        
        # Ensure consistent format
        if isinstance(result, dict) and 'content' in result:
            return result
        else:
            # Convert to expected format
            return {
                'url': url,
                'content': str(result),
                'metadata': {
                    'source_type': 'web',
                    'source_url': url
                },
                'success': True,
                'error': None
            }
    
    async def _ingest_file_or_directory(self, source: str, **kwargs) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """Ingest content from a file or directory."""
        path = Path(source)
        
        if path.is_file():
            logger.info(f"Ingesting file: {source}")
            self.stats['file_sources'] += 1
            self.stats['total_processed'] += 1
            return await self.file_handler.ingest_file(source)
        
        elif path.is_dir():
            logger.info(f"Ingesting directory: {source}")
            results = await self.file_handler.ingest_directory(
                source,
                recursive=kwargs.get('recursive', True),
                file_extensions=kwargs.get('file_extensions')
            )
            self.stats['file_sources'] += len(results)
            self.stats['total_processed'] += len(results)
            return results
        
        else:
            raise ValueError(f"Path exists but is neither file nor directory: {source}")
    
    def get_stats(self) -> Dict[str, int]:
        """Get processing statistics."""
        return self.stats.copy()
    
    def reset_stats(self):
        """Reset processing statistics."""
        self.stats = {
            'total_processed': 0,
            'web_sources': 0,
            'file_sources': 0,
            'errors': 0
        }
    
    def get_supported_file_extensions(self) -> List[str]:
        """Get list of supported file extensions."""
        return self.file_handler.get_supported_extensions()


# Progress tracking for batch operations
class ProgressTracker:
    """Track and report progress for batch ingestion operations."""
    
    def __init__(self, total: int, callback=None):
        """
        Initialize progress tracker.
        
        Args:
            total: Total number of items to process
            callback: Optional callback function(current, total, message)
        """
        self.total = total
        self.current = 0
        self.callback = callback
        self.errors = []
        self.completed = []
    
    def update(self, source: str, success: bool, error: Optional[str] = None):
        """Update progress for a source."""
        self.current += 1
        
        if success:
            self.completed.append(source)
        else:
            self.errors.append((source, error))
        
        if self.callback:
            message = f"Processed {source} - {'Success' if success else f'Error: {error}'}"
            self.callback(self.current, self.total, message)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary of processing."""
        return {
            'total': self.total,
            'completed': len(self.completed),
            'errors': len(self.errors),
            'error_details': self.errors
        }


# Convenience functions
async def ingest_mixed_sources(sources: List[str], **kwargs) -> List[Dict[str, Any]]:
    """
    Ingest a mixed list of URLs and file paths.
    
    Args:
        sources: List of URLs and/or file paths
        **kwargs: Arguments passed to UnifiedIngestionPipeline
        
    Returns:
        List of processed documents
    """
    pipeline = UnifiedIngestionPipeline(**kwargs)
    return await pipeline.batch_ingest(sources)


async def ingest_with_progress(sources: List[str], 
                             progress_callback=None,
                             **kwargs) -> List[Dict[str, Any]]:
    """
    Ingest sources with progress tracking.
    
    Args:
        sources: List of sources to ingest
        progress_callback: Function to call with progress updates
        **kwargs: Arguments for pipeline
        
    Returns:
        List of results with progress tracking
    """
    tracker = ProgressTracker(len(sources), progress_callback)
    pipeline = UnifiedIngestionPipeline(**kwargs)
    
    results = []
    for source in sources:
        try:
            result = await pipeline.ingest(source)
            tracker.update(source, True)
            results.append(result)
        except Exception as e:
            tracker.update(source, False, str(e))
            results.append({
                'url': source,
                'success': False,
                'error': str(e)
            })
    
    return results