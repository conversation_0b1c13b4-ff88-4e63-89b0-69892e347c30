# Enhanced RAG System

## Overview
This enhanced RAG (Retrieval-Augmented Generation) system extends the existing web-based RAG pipeline to support local file ingestion, with a focus on PDF and Markdown documents. Built on top of the existing Supabase vector storage infrastructure, it leverages LlamaIndex for robust document processing.

## Key Features
- **Unified Ingestion** - Single interface for both web URLs and local files
- **PDF Support** - Full PDF processing with structure preservation
- **Enhanced Markdown** - Advanced markdown parsing with metadata extraction
- **Smart Chunking** - Document-aware chunking strategies
- **Rich Metadata** - Comprehensive metadata for better retrieval
- **LlamaIndex Integration** - Leverages proven document processing capabilities

## Architecture

```
enhanced_rag/
├── ingestion/
│   ├── file_ingestion.py      # LlamaIndex-based file readers
│   ├── unified_ingestion.py   # Routing and orchestration
│   └── batch_processor.py     # Bulk file processing
├── parsing/
│   ├── document_parsing.py    # PDF structure extraction
│   ├── markdown_parser.py     # Enhanced markdown parsing
│   └── metadata_extractor.py  # Document metadata extraction
├── chunking/
│   ├── document_chunking.py   # Smart chunking strategies
│   └── context_manager.py     # Context window management
├── storage/
│   ├── enhanced_metadata.py   # Extended metadata schema
│   └── document_store.py      # Document-specific storage
├── query/
│   ├── document_search.py     # File-aware search
│   └── citation_generator.py  # Source attribution
└── utils/
    ├── file_validators.py     # File type validation
    └── progress_tracker.py    # Processing progress
```

## Quick Start

```python
from enhanced_rag import UnifiedRAGPipeline

# Initialize pipeline
pipeline = UnifiedRAGPipeline()

# Ingest a PDF file
await pipeline.ingest("path/to/document.pdf")

# Ingest a Markdown file
await pipeline.ingest("path/to/notes.md")

# Ingest a web URL (backward compatible)
await pipeline.ingest("https://example.com")

# Query across all sources
results = await pipeline.query("What are the key findings?")
```

## Supported File Types
- **PDF** (.pdf) - Full support with structure extraction
- **Markdown** (.md, .markdown) - Enhanced parsing with frontmatter
- **Text** (.txt) - Basic text extraction
- **DOCX** (.docx) - Microsoft Word documents (planned)
- **CSV** (.csv) - Structured data support (planned)

## Installation

```bash
# Install enhanced dependencies
pip install llama-index
pip install llama-index-readers-file
pip install pypdf2
pip install pdfplumber
pip install python-frontmatter

# Configure environment
cp .env.example .env
# Edit .env with your API keys
```

## Configuration

```python
# config.py
ENHANCED_RAG_CONFIG = {
    "file_ingestion": {
        "max_file_size_mb": 100,
        "allowed_extensions": [".pdf", ".md", ".txt"],
        "chunk_size": 1000,
        "chunk_overlap": 200
    },
    "pdf_processing": {
        "extract_tables": True,
        "extract_images": False,
        "preserve_layout": True
    },
    "metadata": {
        "extract_headers": True,
        "extract_frontmatter": True,
        "generate_summaries": True
    }
}
```

## Development Status

See [TASK.md](TASK.md) for current development progress and [PLAN.md](PLAN.md) for the complete development roadmap.

### Current Phase: Basic File Support
- Implementing core file ingestion with LlamaIndex
- Creating unified pipeline interface
- Building initial PDF and Markdown support

## Contributing

1. Check [TASK.md](TASK.md) for available tasks
2. Follow the existing code structure
3. Add tests for new functionality
4. Update documentation as needed

## Testing

```bash
# Run unit tests
pytest tests/

# Run integration tests
pytest tests/integration/

# Run with coverage
pytest --cov=enhanced_rag tests/
```

## Performance Considerations

- Large PDFs are processed in chunks to manage memory
- Batch processing available for multiple files
- Async operations for improved throughput
- Caching layer for repeated operations

## Future Enhancements

- OCR support for scanned PDFs
- Image extraction and analysis
- Support for Office documents (Excel, PowerPoint)
- Real-time document monitoring
- Collaborative annotation system

## License

[Same as parent project]